# 模考卷处理系统使用说明

## 🚀 快速开始

### 1. 环境要求
- Windows 10/11
- .NET 8.0 SDK
- Visual Studio 2022 (推荐) 或 Visual Studio Code

### 2. 打开项目
1. 双击 `ExamPaperProcessor.sln` 文件在Visual Studio中打开项目
2. 或者运行 `build.bat` 脚本进行构建

### 3. 运行程序
- 在Visual Studio中按 F5 运行
- 或者在命令行中运行 `dotnet run`

## 📋 功能使用

### 左侧区域管理
程序左侧显示了一个树形结构，包含：
- **16个区域**: 海淀区、东城区、西城区等
- **5个年份**: 2021年到2025年
- **2种模式**: 一模和二模

每个试卷条目右侧有一个"上传"按钮。

### 上传试卷文档
1. 点击对应试卷的"上传"按钮
2. 选择docx格式的试卷文件
3. 程序会自动解析文档内容
4. 解析完成后，右侧会显示题目预览

### 编辑题目内容
在右侧预览区域，你可以：
- 修改题目内容
- 编辑答案
- 更新解析
- 所有修改会实时保存

### 导出合集
1. 上传完所有需要的试卷后
2. 点击左下角的"导出所有试卷"按钮
3. 选择保存位置和文件名
4. 程序会生成包含目录、试卷和答案的完整文档

## 📄 支持的文档格式

### 输入格式要求
- **文件格式**: .docx (Word文档)
- **题目结构**: 19道题目
  - 1-10题: 选择题
  - 11-15题: 填空题
  - 16-19题: 大题
- **题号格式**: "1." "2." 等数字加点的格式
- **答案格式**: 包含"答案"或"【答案】"标识
- **解析格式**: 包含"解析"或"【解析】"标识

### 数学公式支持
- 支持MathType公式识别
- 公式会显示为占位符文本
- 保持公式在题目中的正确位置

## 📤 导出文档特性

### 自动排版
- **选择题和填空题**: 紧密排布，节省空间
- **大题**: 每题后预留10行答题空间
- **智能分页**: 避免题目跨页显示
- **试卷分离**: 每套试卷独立分页

### 文档结构
1. **目录页**: 自动生成，包含实际页码
2. **试卷内容**: 按时间-模式-区域顺序排列
3. **答案部分**: 可选择仅答案或答案+解析

### 页面设置
- **页眉**: 显示"模考卷合集"
- **页码**: 自动编号，居中显示
- **标题**: 每套试卷都有标题页

## 🔧 故障排除

### 常见问题

**Q: 程序无法启动**
A: 确保已安装.NET 8.0 SDK，运行 `build.bat` 检查构建错误

**Q: 文档解析失败**
A: 检查文档格式是否正确，确保题号使用"1."格式

**Q: 数学公式显示异常**
A: 目前MathType公式显示为占位符，这是正常现象

**Q: 导出文档格式不正确**
A: 检查是否有足够的磁盘空间，确保有Word文档的写入权限

### 性能优化建议
- 一次不要上传过多大文件
- 定期清理临时文件
- 确保有足够的内存空间

## 📞 技术支持

如遇到问题，请提供以下信息：
1. 错误信息截图
2. 使用的文档样例
3. 操作步骤描述

## 🔄 版本更新

当前版本: 1.0.0

### 已实现功能
- ✅ 基础文档解析
- ✅ 题目编辑功能
- ✅ 智能导出排版
- ✅ 目录自动生成
- ✅ 页眉页码支持

### 计划功能
- 🔄 增强MathType支持
- 🔄 批量操作功能
- 🔄 模板定制
- 🔄 云端同步

---

**祝你使用愉快！** 📚✨
