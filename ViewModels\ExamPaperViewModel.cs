using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using ExamPaperProcessor.Models;

namespace ExamPaperProcessor.ViewModels
{
    public class ExamPaperViewModel : ObservableObject
    {
        private string _district = string.Empty;
        private int _year;
        private ExamType _examType;
        private string _filePath = string.Empty;
        private bool _isUploaded;

        public string District
        {
            get => _district;
            set => SetProperty(ref _district, value);
        }

        public int Year
        {
            get => _year;
            set => SetProperty(ref _year, value);
        }

        public ExamType ExamType
        {
            get => _examType;
            set => SetProperty(ref _examType, value);
        }

        public string FilePath
        {
            get => _filePath;
            set => SetProperty(ref _filePath, value);
        }

        public bool IsUploaded
        {
            get => _isUploaded;
            set
            {
                SetProperty(ref _isUploaded, value);
                OnPropertyChanged(nameof(IsNotUploaded));
            }
        }

        public bool IsNotUploaded => !IsUploaded;

        public ObservableCollection<QuestionViewModel> Questions { get; } = new();

        public string DisplayName => $"{Year}{District}高三{(ExamType == ExamType.FirstMock ? "一模" : "二模")}";
    }

    public class DistrictViewModel : ObservableObject
    {
        private string _name = string.Empty;

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public ObservableCollection<YearViewModel> Years { get; } = new();
    }

    public class YearViewModel : ObservableObject
    {
        private int _year;

        public int Year
        {
            get => _year;
            set => SetProperty(ref _year, value);
        }

        public string DisplayName => $"{Year}年";

        public ObservableCollection<ExamPaperViewModel> ExamPapers { get; } = new();
    }

    public class QuestionViewModel : ObservableObject
    {
        private int _number;
        private QuestionType _type;
        private string _content = string.Empty;
        private string _answer = string.Empty;
        private string _explanation = string.Empty;

        public QuestionViewModel() { }

        public QuestionViewModel(Question question)
        {
            Number = question.Number;
            Type = question.Type;
            Content = question.Content;
            Answer = question.Answer;
            Explanation = question.Explanation;
        }

        public int Number
        {
            get => _number;
            set => SetProperty(ref _number, value);
        }

        public QuestionType Type
        {
            get => _type;
            set => SetProperty(ref _type, value);
        }

        public string Content
        {
            get => _content;
            set => SetProperty(ref _content, value);
        }

        public string Answer
        {
            get => _answer;
            set => SetProperty(ref _answer, value);
        }

        public string Explanation
        {
            get => _explanation;
            set => SetProperty(ref _explanation, value);
        }

        public Question ToModel()
        {
            return new Question
            {
                Number = Number,
                Type = Type,
                Content = Content,
                Answer = Answer,
                Explanation = Explanation
            };
        }
    }
}
