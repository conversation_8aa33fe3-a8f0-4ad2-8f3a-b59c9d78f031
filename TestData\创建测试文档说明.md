# 测试文档创建说明

## 如何创建测试用的Word文档

由于程序需要解析.docx格式的文档，你需要手动创建一个测试文档来验证功能。

### 文档格式要求

1. **文件格式**: 保存为.docx格式
2. **题目数量**: 19道题目
3. **题目分布**:
   - 1-10题: 选择题
   - 11-15题: 填空题  
   - 16-19题: 大题

### 题目格式示例

```
1. 下列关于函数的说法正确的是（）
A. 函数的定义域可以为空集
B. 函数的值域一定是实数集
C. 一个函数只能有一个解析式
D. 函数的图像与x轴的交点个数等于函数零点的个数

答案：D
解析：函数零点的定义是使函数值为0的自变量的值，对应函数图像与x轴的交点。

2. 已知集合A={1,2,3}，B={2,3,4}，则A∩B=（）
A. {1}
B. {2,3}
C. {4}
D. {1,2,3,4}

答案：B
解析：两个集合的交集是同时属于两个集合的元素组成的集合。

...继续到第10题...

11. 函数f(x)=x²-4x+3的最小值是_______。

答案：-1
解析：配方得f(x)=(x-2)²-1，所以最小值为-1。

...继续到第15题...

16. 解不等式：2x-3 > x+1

解：移项得：2x-x > 1+3
化简得：x > 4
所以不等式的解集为(4,+∞)。

...继续到第19题...
```

### 创建步骤

1. 打开Microsoft Word
2. 按照上述格式创建19道题目
3. 确保每道题都有题号（如"1."）
4. 为每道题添加答案和解析部分
5. 保存为.docx格式

### 测试建议

- 可以参考`sample_exam.txt`中的内容
- 确保题号格式正确（数字+点）
- 答案和解析部分要有明确的标识
- 可以包含一些数学符号来测试公式处理

### 文件命名建议

建议按照以下格式命名测试文件：
- `2025海淀区高三一模.docx`
- `2024朝阳区高三二模.docx`

这样可以更好地测试程序的完整功能。
