<Window x:Class="ExamPaperProcessor.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:ExamPaperProcessor.ViewModels"
        Title="模考卷处理系统" Height="800" Width="1400"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="400"/>
            <ColumnDefinition Width="5"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧区域列表 -->
        <Border Grid.Column="0" BorderBrush="#E0E0E0" BorderThickness="0,0,1,0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="模考卷管理" FontSize="18" FontWeight="Bold" 
                          Margin="10" HorizontalAlignment="Center"/>

                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <TreeView x:Name="ExamTreeView" ItemsSource="{Binding Districts}">
                        <TreeView.Resources>
                            <HierarchicalDataTemplate DataType="{x:Type local:DistrictViewModel}"
                                                    ItemsSource="{Binding Years}">
                                <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                            </HierarchicalDataTemplate>

                            <HierarchicalDataTemplate DataType="{x:Type local:YearViewModel}"
                                                    ItemsSource="{Binding ExamPapers}">
                                <TextBlock Text="{Binding DisplayName}" FontSize="12"/>
                            </HierarchicalDataTemplate>

                            <DataTemplate DataType="{x:Type local:ExamPaperViewModel}">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0" Text="{Binding DisplayName}" 
                                              FontSize="11" VerticalAlignment="Center"/>
                                    
                                    <Button Grid.Column="1" Content="上传" Width="50" Height="25"
                                           Command="{Binding DataContext.UploadCommand, 
                                                   RelativeSource={RelativeSource AncestorType=Window}}"
                                           CommandParameter="{Binding}"
                                           IsEnabled="{Binding IsNotUploaded}"/>
                                </Grid>
                            </DataTemplate>
                        </TreeView.Resources>
                    </TreeView>
                </ScrollViewer>

                <Button Grid.Row="2" Content="导出所有试卷" Margin="10" Height="40"
                       Command="{Binding ExportCommand}" FontSize="14" FontWeight="Bold"/>
            </Grid>
        </Border>

        <!-- 分隔线 -->
        <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="#E0E0E0"/>

        <!-- 右侧预览区域 -->
        <Grid Grid.Column="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <Border Grid.Row="0" Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
                <TextBlock Text="{Binding CurrentExamPaper.DisplayName, StringFormat='当前编辑: {0}'}" 
                          FontSize="16" FontWeight="Bold" Margin="15,10"/>
            </Border>

            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="10">
                <ItemsControl ItemsSource="{Binding CurrentQuestions}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border BorderBrush="#E0E0E0" BorderThickness="1" Margin="0,5" Padding="10">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0" Text="{Binding Number, StringFormat='题目 {0}'}" 
                                              FontWeight="Bold" FontSize="14" Margin="0,0,0,5"/>

                                    <TextBox Grid.Row="1" Text="{Binding Content, UpdateSourceTrigger=PropertyChanged}" 
                                            TextWrapping="Wrap" MinHeight="60" Margin="0,0,0,5"
                                            AcceptsReturn="True"/>

                                    <TextBox Grid.Row="2" Text="{Binding Answer, UpdateSourceTrigger=PropertyChanged}" 
                                            TextWrapping="Wrap" MinHeight="30" Margin="0,0,0,5"
                                            Background="#E8F5E8"/>

                                    <TextBox Grid.Row="3" Text="{Binding Explanation, UpdateSourceTrigger=PropertyChanged}" 
                                            TextWrapping="Wrap" MinHeight="40"
                                            Background="#FFF8E1"/>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>
        </Grid>
    </Grid>
</Window>
