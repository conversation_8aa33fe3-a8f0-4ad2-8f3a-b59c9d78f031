using System;
using System.Windows;
using ExamPaperProcessor.Tests;

namespace ExamPaperProcessor
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // 在调试模式下运行测试
            #if DEBUG
            try
            {
                DocumentParserTests.RunTests();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"测试运行出错: {ex.Message}", "测试错误",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            #endif
        }
    }
}
