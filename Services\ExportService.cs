using System;
using System.IO;
using System.Linq;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using ExamPaperProcessor.ViewModels;
using ExamPaperProcessor.Models;

namespace ExamPaperProcessor.Services
{
    public class ExportService
    {
        public async Task ExportPapersAsync(List<ExamPaperViewModel> papers, string outputPath,
            IProgress<string>? progress = null)
        {
            if (papers == null || !papers.Any())
                throw new ArgumentException("没有可导出的试卷", nameof(papers));

            if (string.IsNullOrWhiteSpace(outputPath))
                throw new ArgumentException("输出路径不能为空", nameof(outputPath));

            var directory = Path.GetDirectoryName(outputPath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            await Task.Run(() => ExportPapers(papers, outputPath, progress));
        }

        private void ExportPapers(List<ExamPaperViewModel> papers, string outputPath,
            IProgress<string>? progress = null)
        {
            using (var document = WordprocessingDocument.Create(outputPath, WordprocessingDocumentType.Document))
            {
                var mainPart = document.AddMainDocumentPart();
                mainPart.Document = new Document();
                var body = mainPart.Document.AppendChild(new Body());

                progress?.Report("正在设置文档格式...");
                // 添加页眉和页脚部分
                AddHeaderFooterParts(document, mainPart);

                progress?.Report("正在生成目录...");
                // 添加目录页
                AddTableOfContents(body, papers);

                // 添加分页符
                AddPageBreak(body);

                var currentPage = 2; // 目录占1页

                // 添加试卷内容
                for (int i = 0; i < papers.Count; i++)
                {
                    var paper = papers[i];
                    progress?.Report($"正在处理试卷 {i + 1}/{papers.Count}: {paper.DisplayName}");

                    var startPage = currentPage;
                    currentPage = AddExamPaper(body, paper, currentPage);
                }

                progress?.Report("正在生成答案部分...");
                // 添加答案部分
                AddAnswerSection(body, papers);

                mainPart.Document.Save();
            }
        }

        private void AddHeaderFooterParts(WordprocessingDocument document, MainDocumentPart mainPart)
        {
            // 添加页眉部分
            var headerPart = mainPart.AddNewPart<HeaderPart>();
            var headerId = mainPart.GetIdOfPart(headerPart);

            var header = new Header();
            var headerParagraph = new Paragraph();
            var headerRun = new Run();
            headerRun.AppendChild(new Text("模考卷合集"));

            var headerRunProps = new RunProperties();
            headerRunProps.AppendChild(new FontSize() { Val = "20" });
            headerRun.PrependChild(headerRunProps);

            headerParagraph.AppendChild(headerRun);

            var headerParaProps = new ParagraphProperties();
            headerParaProps.AppendChild(new Justification() { Val = JustificationValues.Right });
            headerParagraph.PrependChild(headerParaProps);

            header.AppendChild(headerParagraph);
            headerPart.Header = header;

            // 添加页脚部分（页码）
            var footerPart = mainPart.AddNewPart<FooterPart>();
            var footerId = mainPart.GetIdOfPart(footerPart);

            var footer = new Footer();
            var footerParagraph = new Paragraph();
            var footerRun = new Run();

            // 添加页码字段
            footerRun.AppendChild(new FieldChar() { FieldCharType = FieldCharValues.Begin });
            footerRun.AppendChild(new FieldCode(" PAGE "));
            footerRun.AppendChild(new FieldChar() { FieldCharType = FieldCharValues.End });

            footerParagraph.AppendChild(footerRun);

            var footerParaProps = new ParagraphProperties();
            footerParaProps.AppendChild(new Justification() { Val = JustificationValues.Center });
            footerParagraph.PrependChild(footerParaProps);

            footer.AppendChild(footerParagraph);
            footerPart.Footer = footer;

            // 将页眉页脚关联到文档
            var sectionProps = new SectionProperties();
            var headerReference = new HeaderReference() { Type = HeaderFooterValues.Default, Id = headerId };
            var footerReference = new FooterReference() { Type = HeaderFooterValues.Default, Id = footerId };

            sectionProps.AppendChild(headerReference);
            sectionProps.AppendChild(footerReference);

            mainPart.Document.Body?.AppendChild(sectionProps);
        }

        private void AddTableOfContents(Body body, List<ExamPaperViewModel> papers)
        {
            // 添加目录标题
            var tocTitle = new Paragraph();
            var tocTitleRun = new Run();
            var tocTitleText = new Text("目录");
            tocTitleRun.AppendChild(tocTitleText);
            
            var tocTitleProps = new RunProperties();
            tocTitleProps.AppendChild(new Bold());
            tocTitleProps.AppendChild(new FontSize() { Val = "32" });
            tocTitleRun.PrependChild(tocTitleProps);
            
            tocTitle.AppendChild(tocTitleRun);
            
            var tocTitleParaProps = new ParagraphProperties();
            tocTitleParaProps.AppendChild(new Justification() { Val = JustificationValues.Center });
            tocTitle.PrependChild(tocTitleParaProps);
            
            body.AppendChild(tocTitle);

            // 添加空行
            body.AppendChild(new Paragraph());

            var currentPage = 2;

            // 添加试卷目录项
            foreach (var paper in papers)
            {
                var tocEntry = CreateTocEntry(paper.DisplayName, currentPage);
                body.AppendChild(tocEntry);
                
                // 估算页数（这里简化处理）
                currentPage += EstimatePageCount(paper);
            }

            // 添加答案目录项
            body.AppendChild(new Paragraph());
            var answerTocEntry = CreateTocEntry("答案", currentPage);
            body.AppendChild(answerTocEntry);
        }

        private Paragraph CreateTocEntry(string title, int pageNumber)
        {
            var paragraph = new Paragraph();
            
            var titleRun = new Run();
            titleRun.AppendChild(new Text(title));
            paragraph.AppendChild(titleRun);

            // 添加制表符和页码
            var tabRun = new Run();
            tabRun.AppendChild(new TabChar());
            paragraph.AppendChild(tabRun);

            var pageRun = new Run();
            pageRun.AppendChild(new Text(pageNumber.ToString()));
            paragraph.AppendChild(pageRun);

            // 设置制表位
            var paraProps = new ParagraphProperties();
            var tabs = new Tabs();
            tabs.AppendChild(new TabStop() 
            { 
                Val = TabStopValues.Right, 
                Position = 9000 // 右对齐制表位
            });
            paraProps.AppendChild(tabs);
            paragraph.PrependChild(paraProps);

            return paragraph;
        }

        private int EstimatePageCount(ExamPaperViewModel paper)
        {
            var totalLines = 0;

            // 标题占用行数
            totalLines += 3; // 标题 + 空行

            foreach (var question in paper.Questions)
            {
                // 计算题目内容行数
                var contentLines = EstimateTextLines(question.Content);
                totalLines += contentLines;

                // 根据题目类型添加额外空间
                switch (question.Type)
                {
                    case QuestionType.MultipleChoice:
                    case QuestionType.FillInBlank:
                        totalLines += 1; // 选择题和填空题之间的间距
                        break;
                    case QuestionType.BigQuestion:
                        totalLines += 10; // 大题的答题空间
                        break;
                }
            }

            // 每页大约45行（考虑页边距）
            var pagesNeeded = Math.Max(1, (int)Math.Ceiling(totalLines / 45.0));
            return pagesNeeded;
        }

        private int EstimateTextLines(string text)
        {
            if (string.IsNullOrWhiteSpace(text)) return 1;

            // 假设每行大约80个字符（包括中文字符）
            var charactersPerLine = 80;
            var textLength = text.Length;

            // 考虑换行符
            var explicitLines = text.Split('\n').Length;
            var wrappedLines = (int)Math.Ceiling((double)textLength / charactersPerLine);

            return Math.Max(explicitLines, wrappedLines);
        }

        private int AddExamPaper(Body body, ExamPaperViewModel paper, int currentPage)
        {
            // 添加试卷标题
            var titleParagraph = new Paragraph();
            var titleRun = new Run();
            var titleText = new Text(paper.DisplayName);
            titleRun.AppendChild(titleText);

            var titleProps = new RunProperties();
            titleProps.AppendChild(new Bold());
            titleProps.AppendChild(new FontSize() { Val = "28" });
            titleRun.PrependChild(titleProps);

            titleParagraph.AppendChild(titleRun);

            var titleParaProps = new ParagraphProperties();
            titleParaProps.AppendChild(new Justification() { Val = JustificationValues.Center });
            titleParagraph.PrependChild(titleParaProps);

            body.AppendChild(titleParagraph);
            body.AppendChild(new Paragraph()); // 空行

            // 添加题目
            foreach (var question in paper.Questions.OrderBy(q => q.Number))
            {
                AddQuestion(body, question);
                
                // 为大题添加答题空间
                if (question.Type == QuestionType.BigQuestion)
                {
                    for (int i = 0; i < 10; i++)
                    {
                        body.AppendChild(new Paragraph());
                    }
                }
            }

            // 添加分页符（每套卷子独立一页）
            AddPageBreak(body);

            return currentPage + EstimatePageCount(paper);
        }

        private void AddQuestion(Body body, QuestionViewModel question)
        {
            var questionParagraph = new Paragraph();
            
            // 题号
            var numberRun = new Run();
            numberRun.AppendChild(new Text($"{question.Number}."));
            var numberProps = new RunProperties();
            numberProps.AppendChild(new Bold());
            numberRun.PrependChild(numberProps);
            questionParagraph.AppendChild(numberRun);

            // 题目内容
            var contentRun = new Run();
            contentRun.AppendChild(new Text($" {question.Content}"));
            questionParagraph.AppendChild(contentRun);

            body.AppendChild(questionParagraph);

            // 为选择题和填空题添加适当间距
            if (question.Type != QuestionType.BigQuestion)
            {
                body.AppendChild(new Paragraph());
            }
        }

        private void AddAnswerSection(Body body, List<ExamPaperViewModel> papers)
        {
            // 添加答案标题
            var answerTitle = new Paragraph();
            var answerTitleRun = new Run();
            var answerTitleText = new Text("答案");
            answerTitleRun.AppendChild(answerTitleText);

            var answerTitleProps = new RunProperties();
            answerTitleProps.AppendChild(new Bold());
            answerTitleProps.AppendChild(new FontSize() { Val = "28" });
            answerTitleRun.PrependChild(answerTitleProps);

            answerTitle.AppendChild(answerTitleRun);

            var answerTitleParaProps = new ParagraphProperties();
            answerTitleParaProps.AppendChild(new Justification() { Val = JustificationValues.Center });
            answerTitle.PrependChild(answerTitleParaProps);

            body.AppendChild(answerTitle);
            body.AppendChild(new Paragraph());

            // 添加每套试卷的答案
            foreach (var paper in papers)
            {
                AddPaperAnswers(body, paper);
            }
        }

        private void AddPaperAnswers(Body body, ExamPaperViewModel paper)
        {
            // 试卷答案标题
            var paperAnswerTitle = new Paragraph();
            var paperAnswerTitleRun = new Run();
            paperAnswerTitleRun.AppendChild(new Text(paper.DisplayName));

            var paperAnswerTitleProps = new RunProperties();
            paperAnswerTitleProps.AppendChild(new Bold());
            paperAnswerTitleProps.AppendChild(new FontSize() { Val = "20" });
            paperAnswerTitleRun.PrependChild(paperAnswerTitleProps);

            paperAnswerTitle.AppendChild(paperAnswerTitleRun);
            body.AppendChild(paperAnswerTitle);
            body.AppendChild(new Paragraph());

            // 添加答案
            foreach (var question in paper.Questions.OrderBy(q => q.Number))
            {
                var answerParagraph = new Paragraph();
                
                var answerRun = new Run();
                answerRun.AppendChild(new Text($"{question.Number}. {question.Answer}"));
                answerParagraph.AppendChild(answerRun);

                body.AppendChild(answerParagraph);

                // 如果有解析，也添加解析
                if (!string.IsNullOrWhiteSpace(question.Explanation))
                {
                    var explanationParagraph = new Paragraph();
                    var explanationRun = new Run();
                    explanationRun.AppendChild(new Text($"解析：{question.Explanation}"));
                    
                    var explanationProps = new RunProperties();
                    explanationProps.AppendChild(new Italic());
                    explanationRun.PrependChild(explanationProps);
                    
                    explanationParagraph.AppendChild(explanationRun);
                    body.AppendChild(explanationParagraph);
                }

                body.AppendChild(new Paragraph()); // 空行
            }

            body.AppendChild(new Paragraph()); // 试卷间空行
        }

        private void AddPageBreak(Body body)
        {
            var paragraph = new Paragraph();
            var run = new Run();
            run.AppendChild(new Break() { Type = BreakValues.Page });
            paragraph.AppendChild(run);
            body.AppendChild(paragraph);
        }
    }
}
