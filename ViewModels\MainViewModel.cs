using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ExamPaperProcessor.Models;
using ExamPaperProcessor.Services;

namespace ExamPaperProcessor.ViewModels
{
    public class MainViewModel : ObservableObject
    {
        private readonly DocumentParserService _documentParser;
        private readonly ExportService _exportService;
        private ExamPaperViewModel? _currentExamPaper;
        private ObservableCollection<QuestionViewModel> _currentQuestions = new();

        public ObservableCollection<DistrictViewModel> Districts { get; } = new();

        public ExamPaperViewModel? CurrentExamPaper
        {
            get => _currentExamPaper;
            set => SetProperty(ref _currentExamPaper, value);
        }

        public ObservableCollection<QuestionViewModel> CurrentQuestions
        {
            get => _currentQuestions;
            set => SetProperty(ref _currentQuestions, value);
        }

        public ICommand UploadCommand { get; }
        public ICommand ExportCommand { get; }

        public MainViewModel()
        {
            _documentParser = new DocumentParserService();
            _exportService = new ExportService();
            
            UploadCommand = new AsyncRelayCommand<ExamPaperViewModel>(UploadExamPaper);
            ExportCommand = new AsyncRelayCommand(ExportAllPapers);

            InitializeDistricts();
        }

        private void InitializeDistricts()
        {
            var districtNames = District.GetAllDistricts();
            var years = new[] { 2021, 2022, 2023, 2024, 2025 };

            foreach (var districtName in districtNames)
            {
                var districtViewModel = new DistrictViewModel { Name = districtName };
                
                foreach (var year in years)
                {
                    var yearViewModel = new YearViewModel { Year = year };
                    
                    // 一模
                    var firstMock = new ExamPaperViewModel
                    {
                        Year = year,
                        District = districtName,
                        ExamType = ExamType.FirstMock
                    };
                    
                    // 二模
                    var secondMock = new ExamPaperViewModel
                    {
                        Year = year,
                        District = districtName,
                        ExamType = ExamType.SecondMock
                    };

                    yearViewModel.ExamPapers.Add(firstMock);
                    yearViewModel.ExamPapers.Add(secondMock);
                    districtViewModel.Years.Add(yearViewModel);
                }
                
                Districts.Add(districtViewModel);
            }
        }

        private async Task UploadExamPaper(ExamPaperViewModel? examPaper)
        {
            if (examPaper == null) return;

            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Filter = "Word Documents (*.docx)|*.docx",
                Title = "选择试卷文件"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    var questions = await _documentParser.ParseDocumentAsync(openFileDialog.FileName);
                    examPaper.Questions.Clear();
                    
                    foreach (var question in questions)
                    {
                        examPaper.Questions.Add(new QuestionViewModel(question));
                    }
                    
                    examPaper.FilePath = openFileDialog.FileName;
                    examPaper.IsUploaded = true;
                    
                    CurrentExamPaper = examPaper;
                    CurrentQuestions.Clear();
                    foreach (var q in examPaper.Questions)
                    {
                        CurrentQuestions.Add(q);
                    }
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"解析文档时出错: {ex.Message}", "错误", 
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
        }

        private async Task ExportAllPapers()
        {
            var uploadedPapers = GetAllUploadedPapers();

            if (!uploadedPapers.Any())
            {
                System.Windows.MessageBox.Show("请先上传至少一份试卷", "提示",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                return;
            }

            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "Word Documents (*.docx)|*.docx",
                Title = "保存导出文件",
                FileName = "模考卷合集.docx"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    // 创建进度报告
                    var progress = new Progress<string>(message =>
                    {
                        // 在实际应用中，这里可以更新UI进度条
                        System.Diagnostics.Debug.WriteLine($"导出进度: {message}");
                    });

                    await _exportService.ExportPapersAsync(uploadedPapers, saveFileDialog.FileName, progress);

                    System.Windows.MessageBox.Show($"导出成功!\n共导出 {uploadedPapers.Count} 份试卷\n文件保存至: {saveFileDialog.FileName}",
                        "完成", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"导出时出错: {ex.Message}\n\n详细信息: {ex.InnerException?.Message}",
                        "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
        }

        private List<ExamPaperViewModel> GetAllUploadedPapers()
        {
            var uploadedPapers = new List<ExamPaperViewModel>();
            
            foreach (var district in Districts)
            {
                foreach (var year in district.Years)
                {
                    foreach (var paper in year.ExamPapers)
                    {
                        if (paper.IsUploaded)
                        {
                            uploadedPapers.Add(paper);
                        }
                    }
                }
            }
            
            // 按照时间-模式-区域排序
            return uploadedPapers
                .OrderBy(p => p.Year)
                .ThenBy(p => p.ExamType)
                .ThenBy(p => District.GetAllDistricts().IndexOf(p.District))
                .ToList();
        }
    }
}
