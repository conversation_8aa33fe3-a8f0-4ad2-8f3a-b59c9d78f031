using System.Collections.ObjectModel;

namespace ExamPaperProcessor.Models
{
    public class District
    {
        public string Name { get; set; } = string.Empty;
        public ObservableCollection<YearGroup> Years { get; set; } = new();

        public static List<string> GetAllDistricts()
        {
            return new List<string>
            {
                "海淀区", "东城区", "西城区", "朝阳区", "丰台区", "石景山区",
                "门头沟区", "房山区", "通州区", "顺义区", "昌平区", "大兴区",
                "怀柔区", "平谷区", "密云区", "延庆区"
            };
        }
    }

    public class YearGroup
    {
        public int Year { get; set; }
        public ExamPaper FirstMock { get; set; } = new();
        public ExamPaper SecondMock { get; set; } = new();

        public YearGroup(int year, string district)
        {
            Year = year;
            FirstMock = new ExamPaper
            {
                Year = year,
                District = district,
                ExamType = ExamType.FirstMock
            };
            SecondMock = new ExamPaper
            {
                Year = year,
                District = district,
                ExamType = ExamType.SecondMock
            };
        }
    }
}
