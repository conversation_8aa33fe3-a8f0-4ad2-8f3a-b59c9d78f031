using System.Text.RegularExpressions;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using ExamPaperProcessor.Models;

namespace ExamPaperProcessor.Services
{
    public class DocumentParserService
    {
        public async Task<List<Question>> ParseDocumentAsync(string filePath)
        {
            return await Task.Run(() => ParseDocument(filePath));
        }

        private List<Question> ParseDocument(string filePath)
        {
            var questions = new List<Question>();

            using (var document = WordprocessingDocument.Open(filePath, false))
            {
                var body = document.MainDocumentPart?.Document.Body;
                if (body == null) return questions;

                var allText = ExtractTextFromBody(body);
                var questionBlocks = SplitIntoQuestions(allText);

                for (int i = 0; i < questionBlocks.Count && i < 19; i++)
                {
                    var question = ParseQuestionBlock(questionBlocks[i], i + 1);
                    if (question != null)
                    {
                        questions.Add(question);
                    }
                }
            }

            return questions;
        }

        private string ExtractTextFromBody(Body body)
        {
            var text = string.Empty;
            
            foreach (var element in body.Elements())
            {
                if (element is Paragraph paragraph)
                {
                    text += ExtractTextFromParagraph(paragraph) + "\n";
                }
                else if (element is Table table)
                {
                    text += ExtractTextFromTable(table) + "\n";
                }
            }

            return text;
        }

        private string ExtractTextFromParagraph(Paragraph paragraph)
        {
            var text = string.Empty;

            foreach (var run in paragraph.Elements<Run>())
            {
                foreach (var element in run.Elements())
                {
                    if (element is Text textElement)
                    {
                        text += textElement.Text;
                    }
                    else if (element is Drawing drawing)
                    {
                        // 处理MathType公式图片
                        var mathText = ExtractMathTypeText(drawing);
                        text += string.IsNullOrEmpty(mathText) ? "[数学公式]" : $"[{mathText}]";
                    }
                    else if (element is Picture picture)
                    {
                        // 处理其他图片
                        text += "[图片]";
                    }
                    else if (element is DocumentFormat.OpenXml.Wordprocessing.Object obj)
                    {
                        // 处理嵌入对象（可能是MathType）
                        text += "[数学公式]";
                    }
                }
            }

            return text;
        }

        private string ExtractMathTypeText(Drawing drawing)
        {
            try
            {
                // 尝试从Drawing中提取MathType的替代文本
                var docProperties = drawing.Descendants<DocumentFormat.OpenXml.Drawing.DocProperties>().FirstOrDefault();
                if (docProperties != null && !string.IsNullOrEmpty(docProperties.Description))
                {
                    return docProperties.Description.Value;
                }

                // 如果没有描述，返回通用占位符
                return "数学公式";
            }
            catch
            {
                return "数学公式";
            }
        }

        private string ExtractTextFromTable(Table table)
        {
            var text = string.Empty;
            
            foreach (var row in table.Elements<TableRow>())
            {
                foreach (var cell in row.Elements<TableCell>())
                {
                    foreach (var paragraph in cell.Elements<Paragraph>())
                    {
                        text += ExtractTextFromParagraph(paragraph) + " ";
                    }
                }
                text += "\n";
            }

            return text;
        }

        private List<string> SplitIntoQuestions(string allText)
        {
            var questions = new List<string>();
            
            // 使用正则表达式匹配题号模式 "1." "2." 等
            var questionPattern = @"(?=\d+\.)";
            var parts = Regex.Split(allText, questionPattern, RegexOptions.Multiline);

            foreach (var part in parts)
            {
                if (!string.IsNullOrWhiteSpace(part) && Regex.IsMatch(part.Trim(), @"^\d+\."))
                {
                    questions.Add(part.Trim());
                }
            }

            return questions;
        }

        private Question? ParseQuestionBlock(string questionText, int questionNumber)
        {
            if (string.IsNullOrWhiteSpace(questionText)) return null;

            var question = new Question
            {
                Number = questionNumber,
                Type = DetermineQuestionType(questionNumber)
            };

            // 分离题目内容、答案和解析
            var sections = SeparateQuestionSections(questionText);
            
            question.Content = sections.Content;
            question.Answer = sections.Answer;
            question.Explanation = sections.Explanation;

            return question;
        }

        private QuestionType DetermineQuestionType(int questionNumber)
        {
            if (questionNumber <= 10)
                return QuestionType.MultipleChoice;
            else if (questionNumber <= 15)
                return QuestionType.FillInBlank;
            else
                return QuestionType.BigQuestion;
        }

        private (string Content, string Answer, string Explanation) SeparateQuestionSections(string questionText)
        {
            var content = string.Empty;
            var answer = string.Empty;
            var explanation = string.Empty;

            // 更智能的分离逻辑
            var lines = questionText.Split('\n', StringSplitOptions.RemoveEmptyEntries);

            var currentSection = "content";

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();

                // 识别答案部分的多种格式
                if (Regex.IsMatch(trimmedLine, @"^(答案[:：]?|【答案】[:：]?|Answer[:：]?)", RegexOptions.IgnoreCase))
                {
                    currentSection = "answer";
                    // 如果答案在同一行，提取答案内容
                    var answerMatch = Regex.Match(trimmedLine, @"(答案[:：]?|【答案】[:：]?|Answer[:：]?)\s*(.+)", RegexOptions.IgnoreCase);
                    if (answerMatch.Success && !string.IsNullOrWhiteSpace(answerMatch.Groups[2].Value))
                    {
                        answer += answerMatch.Groups[2].Value + "\n";
                    }
                    continue;
                }
                // 识别解析部分的多种格式
                else if (Regex.IsMatch(trimmedLine, @"^(解析[:：]?|【解析】[:：]?|分析[:：]?|【分析】[:：]?|Solution[:：]?)", RegexOptions.IgnoreCase))
                {
                    currentSection = "explanation";
                    // 如果解析在同一行，提取解析内容
                    var explanationMatch = Regex.Match(trimmedLine, @"(解析[:：]?|【解析】[:：]?|分析[:：]?|【分析】[:：]?|Solution[:：]?)\s*(.+)", RegexOptions.IgnoreCase);
                    if (explanationMatch.Success && !string.IsNullOrWhiteSpace(explanationMatch.Groups[2].Value))
                    {
                        explanation += explanationMatch.Groups[2].Value + "\n";
                    }
                    continue;
                }

                // 根据当前部分添加内容
                switch (currentSection)
                {
                    case "content":
                        // 跳过空行和只包含题号的行
                        if (!string.IsNullOrWhiteSpace(trimmedLine) && !Regex.IsMatch(trimmedLine, @"^\d+\.$"))
                        {
                            content += trimmedLine + "\n";
                        }
                        break;
                    case "answer":
                        if (!string.IsNullOrWhiteSpace(trimmedLine))
                        {
                            answer += trimmedLine + "\n";
                        }
                        break;
                    case "explanation":
                        if (!string.IsNullOrWhiteSpace(trimmedLine))
                        {
                            explanation += trimmedLine + "\n";
                        }
                        break;
                }
            }

            // 清理内容
            content = CleanQuestionContent(content.Trim());
            answer = answer.Trim();
            explanation = explanation.Trim();

            return (content, answer, explanation);
        }

        private string CleanQuestionContent(string content)
        {
            if (string.IsNullOrWhiteSpace(content)) return content;

            // 移除题号（如果在开头）
            content = Regex.Replace(content, @"^\d+\.\s*", "");

            // 清理多余的空白字符
            content = Regex.Replace(content, @"\s+", " ");

            return content.Trim();
        }
    }
}
