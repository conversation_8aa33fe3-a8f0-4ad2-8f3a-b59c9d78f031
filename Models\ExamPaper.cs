using System.Collections.ObjectModel;
using System.ComponentModel;

namespace ExamPaperProcessor.Models
{
    public class ExamPaper : INotifyPropertyChanged
    {
        private string _district = string.Empty;
        private int _year;
        private ExamType _examType;
        private string _filePath = string.Empty;
        private bool _isUploaded;
        private ObservableCollection<Question> _questions = new();

        public string District
        {
            get => _district;
            set
            {
                _district = value;
                OnPropertyChanged(nameof(District));
                OnPropertyChanged(nameof(DisplayName));
            }
        }

        public int Year
        {
            get => _year;
            set
            {
                _year = value;
                OnPropertyChanged(nameof(Year));
                OnPropertyChanged(nameof(DisplayName));
            }
        }

        public ExamType ExamType
        {
            get => _examType;
            set
            {
                _examType = value;
                OnPropertyChanged(nameof(ExamType));
                OnPropertyChanged(nameof(DisplayName));
            }
        }

        public string FilePath
        {
            get => _filePath;
            set
            {
                _filePath = value;
                OnPropertyChanged(nameof(FilePath));
            }
        }

        public bool IsUploaded
        {
            get => _isUploaded;
            set
            {
                _isUploaded = value;
                OnPropertyChanged(nameof(IsUploaded));
            }
        }

        public ObservableCollection<Question> Questions
        {
            get => _questions;
            set
            {
                _questions = value;
                OnPropertyChanged(nameof(Questions));
            }
        }

        public string DisplayName => $"{Year}{District}高三{(ExamType == ExamType.FirstMock ? "一模" : "二模")}";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public enum ExamType
    {
        FirstMock,  // 一模
        SecondMock  // 二模
    }

    public class Question : INotifyPropertyChanged
    {
        private int _number;
        private QuestionType _type;
        private string _content = string.Empty;
        private string _answer = string.Empty;
        private string _explanation = string.Empty;
        private List<MathElement> _mathElements = new();

        public int Number
        {
            get => _number;
            set
            {
                _number = value;
                OnPropertyChanged(nameof(Number));
            }
        }

        public QuestionType Type
        {
            get => _type;
            set
            {
                _type = value;
                OnPropertyChanged(nameof(Type));
            }
        }

        public string Content
        {
            get => _content;
            set
            {
                _content = value;
                OnPropertyChanged(nameof(Content));
            }
        }

        public string Answer
        {
            get => _answer;
            set
            {
                _answer = value;
                OnPropertyChanged(nameof(Answer));
            }
        }

        public string Explanation
        {
            get => _explanation;
            set
            {
                _explanation = value;
                OnPropertyChanged(nameof(Explanation));
            }
        }

        public List<MathElement> MathElements
        {
            get => _mathElements;
            set
            {
                _mathElements = value;
                OnPropertyChanged(nameof(MathElements));
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public enum QuestionType
    {
        MultipleChoice,  // 选择题 (1-10)
        FillInBlank,     // 填空题 (11-15)
        BigQuestion      // 大题 (16-19)
    }

    public class MathElement
    {
        public string ImagePath { get; set; } = string.Empty;
        public int Position { get; set; }  // 在文本中的位置
        public string PlaceholderText { get; set; } = string.Empty;  // 占位符文本
    }
}
