# 模考卷处理系统

这是一个用于处理高三模考试卷的WPF应用程序，可以解析Word文档格式的试卷，并将多份试卷合并导出为统一格式的文档。

## 功能特性

### 📋 试卷管理
- 支持16个区域：海淀区、东城区、西城区、朝阳区、丰台区、石景山区、门头沟区、房山区、通州区、顺义区、昌平区、大兴区、怀柔区、平谷区、密云区、延庆区
- 支持2021-2025年的一模和二模试卷
- 按时间-模式-区域的层次结构组织

### 📄 文档解析
- 自动解析docx格式的试卷文档
- 智能识别19道题目结构：
  - 1-10题：选择题
  - 11-15题：填空题
  - 16-19题：大题
- 支持MathType数学公式的处理
- 自动分离题目内容、答案和解析

### ✏️ 编辑功能
- 实时预览解析后的题目
- 支持手动编辑题目内容、答案和解析
- 直观的用户界面

### 📤 导出功能
- 智能排版：选择题和填空题紧密排布
- 大题自动预留10行答题空间
- 智能分页：避免题目跨页显示
- 每套试卷独立分页
- 自动生成页眉和页码
- 自动生成目录（包含实际页码）
- 答案部分支持仅答案或答案+解析模式

## 技术架构

### 🏗️ 框架和库
- **UI框架**: WPF (.NET 8)
- **MVVM框架**: Microsoft.Toolkit.Mvvm
- **文档处理**: DocumentFormat.OpenXml
- **文档解析**: DocX

### 📁 项目结构
```
ExamPaperProcessor/
├── Models/                 # 数据模型
│   ├── ExamPaper.cs       # 试卷模型
│   └── District.cs        # 区域模型
├── ViewModels/            # 视图模型
│   ├── MainViewModel.cs   # 主视图模型
│   └── ExamPaperViewModel.cs # 试卷视图模型
├── Services/              # 服务层
│   ├── DocumentParserService.cs # 文档解析服务
│   └── ExportService.cs   # 导出服务
├── MainWindow.xaml        # 主界面
├── App.xaml              # 应用程序
└── ExamPaperProcessor.csproj # 项目文件
```

## 使用说明

### 🚀 启动项目
1. 在Visual Studio中打开 `ExamPaperProcessor.sln`
2. 确保已安装.NET 8 SDK
3. 按F5运行项目

### 📝 上传试卷
1. 在左侧树形结构中找到对应的年份、区域和模式
2. 点击"上传"按钮
3. 选择docx格式的试卷文件
4. 系统会自动解析并在右侧显示预览

### ✏️ 编辑题目
1. 在右侧预览区域可以直接编辑题目内容
2. 支持修改题目、答案和解析
3. 修改会实时保存

### 📤 导出试卷
1. 上传完所有需要的试卷后
2. 点击"导出所有试卷"按钮
3. 选择保存位置
4. 系统会生成包含目录、试卷和答案的完整文档

## 开发说明

### 🔧 核心算法
- **文档解析**: 使用OpenXML解析Word文档，识别段落、表格和图片
- **题目分割**: 基于正则表达式识别题号模式
- **智能分页**: 根据内容长度和题目类型进行分页计算
- **MathType处理**: 识别并保持数学公式的位置

### 🎯 性能优化
- 异步文档处理避免UI阻塞
- 大文件分块处理
- 内存优化的文档生成

### 🐛 已知限制
- MathType公式目前显示为占位符，需要进一步优化
- 页码计算基于估算，可能需要微调
- 复杂表格布局可能需要手动调整

## 后续开发计划

1. **增强MathType支持**: 更好地处理数学公式的显示和导出
2. **页面布局优化**: 更精确的分页算法
3. **批量操作**: 支持批量上传和处理
4. **模板定制**: 允许用户自定义导出模板
5. **云端同步**: 支持试卷数据的云端存储和同步

## 技术支持

如有问题或建议，请联系开发团队。
